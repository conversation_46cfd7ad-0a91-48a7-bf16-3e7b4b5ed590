import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { successResponse, errorResponse } from '@/lib/api-utils'

// GET /api/health - Health check endpoint
export async function GET(request: NextRequest) {
  try {
    // Test database connection
    await prisma.$queryRaw`SELECT 1`
    
    // Get basic stats
    const [toolCount, materialCount, userCount] = await Promise.all([
      prisma.tool.count().catch(() => 0),
      prisma.material.count().catch(() => 0),
      prisma.user.count().catch(() => 0)
    ])

    return successResponse({
      status: 'healthy',
      database: 'connected',
      timestamp: new Date().toISOString(),
      stats: {
        tools: toolCount,
        materials: materialCount,
        users: userCount
      }
    })
  } catch (error) {
    console.error('Health check failed:', error)
    return errorResponse('Service unhealthy', 503)
  }
}
