import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { UpdateUserSchema } from '@/lib/validations'
import {
  successResponse,
  errorResponse,
  validateRequest,
  handleDatabaseError,
  logActivity
} from '@/lib/api-utils'

// GET /api/users/[id] - Get user by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            borrowingTransactions: {
              where: { status: 'ACTIVE' }
            },
            consumptionTransactions: true,
          },
        },
        borrowingTransactions: {
          where: { status: 'ACTIVE' },
          include: {
            borrowingItems: {
              include: {
                tool: {
                  select: { name: true }
                }
              }
            }
          },
          orderBy: { dueDate: 'asc' },
          take: 5,
        },
      },
    })

    if (!user) {
      return errorResponse('User not found', 404)
    }

    return successResponse(user)
  } catch (error) {
    console.error('Error fetching user:', error)
    return handleDatabaseError(error)
  }
}

// PUT /api/users/[id] - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const validation = await validateRequest(request, UpdateUserSchema)
    if (!validation.success) {
      return validation.response
    }

    // Get existing user
    const existingUser = await prisma.user.findUnique({
      where: { id },
    })

    if (!existingUser) {
      return errorResponse('User not found', 404)
    }

    // Check if email is being changed and if it conflicts
    if (validation.data.email && validation.data.email !== existingUser.email) {
      const emailConflict = await prisma.user.findUnique({
        where: { email: validation.data.email },
      })

      if (emailConflict) {
        return errorResponse('User with this email already exists', 409)
      }
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id },
      data: validation.data,
      include: {
        _count: {
          select: {
            borrowingTransactions: {
              where: { status: 'ACTIVE' }
            },
            consumptionTransactions: true,
          },
        },
      },
    })

    // Log activity
    await logActivity('USER', id, 'UPDATE', undefined, existingUser, updatedUser)

    return successResponse(updatedUser, 'User updated successfully')
  } catch (error) {
    console.error('Error updating user:', error)
    return handleDatabaseError(error)
  }
}

// DELETE /api/users/[id] - Delete user (soft delete by setting isActive to false)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            borrowingTransactions: {
              where: { status: { in: ['ACTIVE', 'OVERDUE'] } }
            },
          },
        },
      },
    })

    if (!user) {
      return errorResponse('User not found', 404)
    }

    // Check if user has active borrowings
    if (user._count.borrowingTransactions > 0) {
      return errorResponse(
        `Cannot delete user. They have ${user._count.borrowingTransactions} active borrowings.`,
        400
      )
    }

    // Soft delete by setting isActive to false
    const updatedUser = await prisma.user.update({
      where: { id },
      data: { isActive: false },
    })

    // Log activity
    await logActivity('USER', id, 'DELETE', undefined, user, updatedUser)

    return successResponse(null, 'User deactivated successfully')
  } catch (error) {
    console.error('Error deleting user:', error)
    return handleDatabaseError(error)
  }
}
